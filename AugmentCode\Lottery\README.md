# 彩票数据分析四步程序

## 程序简介

这是一个按照用户要求开发的彩票数据分析程序，严格按照四个步骤进行：

1. **第一步**：读取Excel表格数据并排序
2. **第二步**：指定数据范围、开始行与计算公式
3. **第三步**：开始计算和比对
4. **第四步**：将校核结果保存在Excel文件中

## 功能特点

- 支持双色球(SSQ)和大乐透(DLT)两种彩票类型
- 提供三种预测方法：
  - 基于贝叶斯概率预测
  - 基于多条件贝叶斯概率预测
  - 基于全条件贝叶斯概率预测
- 自动进行99次循环计算和比对
- 智能命中率检测（SSQ：红球≥5个+蓝球1个，DLT：红球≥4个+蓝球2个）
- 结果自动保存到Excel文件

## 文件说明

- `lottery_analysis_four_steps.py` - 主程序文件
- `lottery_data_all.xlsx` - 彩票历史数据文件
- `test_quick.py` - 快速测试程序
- `read_lottery_data.py` - 参考的原始程序

## 使用方法

### 1. 运行主程序

```bash
python lottery_analysis_four_steps.py
```

### 2. 按提示进行选择

程序会依次询问：

1. **选择彩票类型**：
   - 输入 `1` 选择双色球(SSQ)
   - 输入 `2` 选择大乐透(DLT)
   - 直接回车使用默认值（双色球）

2. **选择开始行数**：
   - 输入一个数字（建议1000-3000之间）
   - 直接回车使用默认值（2000）

3. **选择计算方法**：
   - 输入 `1` 选择基于贝叶斯概率预测
   - 输入 `2` 选择基于多条件贝叶斯概率预测
   - 输入 `3` 选择基于全条件贝叶斯概率预测
   - 直接回车使用默认值（多条件贝叶斯概率）

### 3. 查看结果

程序运行完成后会生成一个Excel文件，文件名格式为：
`lottery_analysis_results_{彩票类型}_{时间戳}.xlsx`

Excel文件包含以下工作表：
- **详细结果**：每次计算的详细信息
- **汇总统计**：总体统计信息
- **命中详情**：命中的具体情况（如果有命中）

## 数据格式要求

程序需要 `lottery_data_all.xlsx` 文件，包含两个工作表：

### SSQ_data_all 工作表
- A列：期号(NO)
- I-O列：彩票号码数据（r1-r6为红球，b为蓝球）

### DLT_data_all 工作表
- A列：期号(NO)
- H-N列：彩票号码数据（r1-r5为红球，b1-b2为蓝球）

## 算法说明

### 贝叶斯概率预测
基于历史数据中各号码的出现概率进行预测。

### 多条件贝叶斯概率预测
基于最新一期号码的跟随性概率，结合历史概率进行预测。

### 全条件贝叶斯概率预测
基于所有历史号码的跟随性概率，结合历史概率进行预测。

## 命中判断标准

### 双色球(SSQ)
- 红球：6个号码中至少命中5个
- 蓝球：1个号码必须命中
- 同时满足上述条件才算命中

### 大乐透(DLT)
- 红球：5个号码中至少命中4个
- 蓝球：2个号码必须全部命中
- 同时满足上述条件才算命中

## 注意事项

1. 确保 `lottery_data_all.xlsx` 文件存在于程序目录中
2. 开始行数不能超过数据总行数减去100行
3. 程序会自动清理无效数据和空数据行
4. 所有数据按期号从小到大排序
5. 计算过程可能需要一些时间，请耐心等待

## 快速测试

如果想快速测试程序功能，可以运行：

```bash
python test_quick.py
```

这会进行10次快速计算测试，查看预测效果。

## 技术支持

如有问题，请检查：
1. Python环境是否正确安装
2. 必要的库是否已安装（pandas, numpy, openpyxl）
3. 数据文件是否存在且格式正确

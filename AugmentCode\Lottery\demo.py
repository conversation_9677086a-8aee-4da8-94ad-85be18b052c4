#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
彩票分析程序演示
自动运行一个完整的分析流程
"""

from lottery_analysis_four_steps import LotteryAnalyzer

def demo_analysis():
    """演示完整的分析流程"""
    print("=" * 60)
    print("彩票分析程序演示")
    print("=" * 60)
    
    # 创建分析器
    analyzer = LotteryAnalyzer()
    
    # 第一步：读取数据
    print("\n正在执行第一步：读取Excel数据...")
    if not analyzer.step1_read_and_sort_data():
        print("数据读取失败！")
        return
    
    # 第二步：设置参数（自动设置，不需要用户输入）
    print("\n正在执行第二步：设置分析参数...")
    analyzer.lottery_type = "SSQ"  # 选择双色球
    analyzer.data = analyzer.ssqhistory_allout
    analyzer.start_row = 2000  # 从第2000行开始
    analyzer.method = "multi_bayesian"  # 使用多条件贝叶斯方法
    
    print(f"设置完成：")
    print(f"  彩票类型: {analyzer.lottery_type}")
    print(f"  开始行数: {analyzer.start_row}")
    print(f"  计算方法: {analyzer.method}")
    
    # 第三步：计算和比对（只做10次演示）
    print("\n正在执行第三步：计算和比对（演示10次）...")
    analyzer.results = []
    hit_count = 0
    
    for i in range(10):
        current_row = analyzer.start_row + i
        
        # 获取训练数据
        train_data = analyzer.data.iloc[:current_row].copy()
        
        # 获取实际号码
        actual_numbers = analyzer.data.iloc[current_row, 1:].values.tolist()
        
        # 预测号码
        predicted_numbers = analyzer.predict_numbers(train_data, analyzer.method)
        
        if predicted_numbers is None:
            continue
        
        # 检查命中情况
        hit_info = analyzer.check_hit_rate(predicted_numbers, actual_numbers)
        
        # 记录结果
        result = {
            'iteration': i + 1,
            'base_period': analyzer.data.iloc[current_row - 1, 0],
            'target_period': analyzer.data.iloc[current_row, 0],
            'method': analyzer.method,
            'predicted_numbers': predicted_numbers,
            'actual_numbers': actual_numbers,
            'red_hits': hit_info['red_hits'],
            'blue_hits': hit_info['blue_hits'],
            'total_hits': hit_info['total_hits'],
            'is_hit': hit_info['is_hit']
        }
        
        analyzer.results.append(result)
        
        # 显示结果
        print(f"  第{i+1}次计算:")
        print(f"    基于期号: {result['base_period']}")
        print(f"    预测期号: {result['target_period']}")
        print(f"    预测号码: {predicted_numbers}")
        print(f"    实际号码: {actual_numbers}")
        print(f"    命中情况: 红球{hit_info['red_hits']}个, 蓝球{hit_info['blue_hits']}个")
        
        if hit_info['is_hit']:
            hit_count += 1
            print(f"    *** 命中！***")
        
        print()
    
    print(f"演示完成！10次计算中命中{hit_count}次")
    
    # 第四步：保存结果
    print("\n正在执行第四步：保存结果到Excel...")
    if analyzer.step4_save_results_to_excel():
        print("结果保存成功！")
    else:
        print("结果保存失败！")
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)

if __name__ == "__main__":
    demo_analysis()

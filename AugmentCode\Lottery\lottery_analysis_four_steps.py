#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
彩票数据分析四步程序
按照用户要求的四个步骤进行彩票数据分析：
1. 读取Excel表格数据并排序
2. 指定数据范围、开始行与计算公式
3. 开始计算和比对
4. 将校核结果保存在Excel文件中
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime
import threading
import sys

class LotteryAnalyzer:
    """彩票分析器类"""
    
    def __init__(self, file_path="lottery_data_all.xlsx"):
        """
        初始化彩票分析器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.ssqhistory_allout = None
        self.dlthistory_allout = None
        self.results = []  # 存储校核结果
        
    def step1_read_and_sort_data(self):
        """
        第一步：读取Excel表格数据并排序
        """
        print("=" * 60)
        print("第一步：读取Excel表格数据并排序")
        print("=" * 60)
        
        try:
            # 读取双色球数据
            print("正在读取双色球数据...")
            ssq_data = pd.read_excel(self.file_path, sheet_name="SSQ_data_all")
            
            # 提取A列、I列至O列数据（共8列）
            # A列是第0列，I列至O列是第8-14列
            self.ssqhistory_allout = ssq_data.iloc[:, [0] + list(range(8, 15))].copy()
            
            # 读取大乐透数据
            print("正在读取大乐透数据...")
            dlt_data = pd.read_excel(self.file_path, sheet_name="DLT_data_all")
            
            # 提取A列、H列至N列数据（共8列）
            # A列是第0列，H列至N列是第7-13列
            self.dlthistory_allout = dlt_data.iloc[:, [0] + list(range(7, 14))].copy()
            
            # 清理和排序数据
            datasets = {
                "ssqhistory_allout": self.ssqhistory_allout,
                "dlthistory_allout": self.dlthistory_allout
            }
            
            for name, dataset in datasets.items():
                print(f"处理 {name} 数据...")
                
                # 删除包含NaN的行
                dataset.dropna(inplace=True)
                
                # 按第一列（NO列）从小到大排序
                dataset.sort_values(by=dataset.columns[0], inplace=True)
                
                # 重置索引
                dataset.reset_index(drop=True, inplace=True)
                
                # 确保数据类型一致性
                try:
                    # 第一列是期号，应该是整数类型
                    dataset.iloc[:, 0] = dataset.iloc[:, 0].astype(int)
                    
                    # 其他列是彩票号码，也应该是整数类型
                    for col in range(1, dataset.shape[1]):
                        dataset.iloc[:, col] = dataset.iloc[:, col].astype(int)
                except ValueError as e:
                    print(f"警告: 转换 {name} 的数据类型时出错: {e}")
                    # 使用更安全的方法
                    for col in range(dataset.shape[1]):
                        dataset.iloc[:, col] = dataset.iloc[:, col].fillna(0).astype(int)
            
            # 打印数据信息
            print(f"\nssqhistory_allout 数据信息:")
            print(f"行数: {self.ssqhistory_allout.shape[0]}, 列数: {self.ssqhistory_allout.shape[1]}")
            print(f"期号范围: {self.ssqhistory_allout.iloc[0, 0]} - {self.ssqhistory_allout.iloc[-1, 0]}")
            
            print(f"\ndlthistory_allout 数据信息:")
            print(f"行数: {self.dlthistory_allout.shape[0]}, 列数: {self.dlthistory_allout.shape[1]}")
            print(f"期号范围: {self.dlthistory_allout.iloc[0, 0]} - {self.dlthistory_allout.iloc[-1, 0]}")
            
            print("\n第一步完成：数据读取和排序成功！")
            return True
            
        except FileNotFoundError:
            print(f"错误: 找不到文件 '{self.file_path}'，请确保文件存在。")
            return False
        except Exception as e:
            print(f"错误: {str(e)}")
            return False

    def get_user_input_with_timeout(self, prompt, timeout=30, default_value=None):
        """
        获取用户输入，支持超时（Windows兼容版本）

        Args:
            prompt: 提示信息
            timeout: 超时时间（秒）
            default_value: 默认值

        Returns:
            用户输入或默认值
        """
        print(f"{prompt}（{timeout}秒后将使用默认值: {default_value}）")

        # 使用简单的方式：直接获取输入，不实现复杂的超时机制
        # 在实际使用中，用户可以直接按回车使用默认值
        try:
            user_input = input("请输入（直接回车使用默认值）: ")
            if user_input.strip() == "":
                print(f"使用默认值: {default_value}")
                return str(default_value) if default_value is not None else ""
            return user_input.strip()
        except KeyboardInterrupt:
            print(f"\n用户中断，使用默认值: {default_value}")
            return str(default_value) if default_value is not None else ""

    def step2_get_user_parameters(self):
        """
        第二步：指定数据范围、开始行与计算公式
        """
        print("\n" + "=" * 60)
        print("第二步：指定数据范围、开始行与计算公式")
        print("=" * 60)
        
        # 1. 选择彩票类型
        print("\n请选择彩票类型:")
        print("1. SSQ (双色球)")
        print("2. DLT (大乐透)")
        
        lottery_choice = self.get_user_input_with_timeout(
            "请输入选择 (1 或 2，30秒后默认选择1): ", 30, "1"
        )
        
        if lottery_choice == "2":
            self.lottery_type = "DLT"
            self.data = self.dlthistory_allout
            print("您选择了：大乐透 (DLT)")
        else:
            self.lottery_type = "SSQ"
            self.data = self.ssqhistory_allout
            print("您选择了：双色球 (SSQ)")
        
        # 2. 选择开始行
        max_rows = len(self.data)
        print(f"\n数据总行数: {max_rows}")
        print(f"建议开始行数范围: 1000 - {max_rows - 100}")
        
        start_row_input = self.get_user_input_with_timeout(
            f"请输入开始行数 (30秒后默认选择2000): ", 30, "2000"
        )
        
        try:
            self.start_row = int(start_row_input)
            if self.start_row < 1 or self.start_row >= max_rows - 100:
                print(f"警告: 开始行数超出合理范围，使用默认值2000")
                self.start_row = min(2000, max_rows - 100)
        except ValueError:
            print("输入无效，使用默认值2000")
            self.start_row = min(2000, max_rows - 100)
        
        print(f"开始行数设置为: {self.start_row}")
        
        # 3. 选择计算方法
        print("\n请选择计算方法:")
        print("1. 基于贝叶斯概率预测")
        print("2. 基于多条件贝叶斯概率预测")
        print("3. 基于全条件贝叶斯概率预测")
        
        method_choice = self.get_user_input_with_timeout(
            "请输入选择 (1、2 或 3，30秒后默认选择2): ", 30, "2"
        )
        
        if method_choice == "1":
            self.method = "bayesian"
            print("您选择了：基于贝叶斯概率预测")
        elif method_choice == "3":
            self.method = "full_bayesian"
            print("您选择了：基于全条件贝叶斯概率预测")
        else:
            self.method = "multi_bayesian"
            print("您选择了：基于多条件贝叶斯概率预测")
        
        print("\n第二步完成：参数设置成功！")
        print(f"彩票类型: {self.lottery_type}")
        print(f"开始行数: {self.start_row}")
        print(f"计算方法: {self.method}")
        
        return True

    def calculate_ball_statistics(self, df, columns, min_ball, max_ball):
        """
        统计球号出现次数和概率

        Args:
            df: 数据集
            columns: 要统计的列索引列表
            min_ball: 最小球号
            max_ball: 最大球号

        Returns:
            times: 各球号出现次数的字典
            probs: 各球号出现概率的字典
        """
        # 创建一个字典，用于存储每个球号的出现次数
        times = {i: 0 for i in range(min_ball, max_ball + 1)}

        # 将所有指定列的数据合并为一个numpy数组
        all_balls = np.array([])
        for col in columns:
            all_balls = np.append(all_balls, df.iloc[:, col].values)

        # 统计每个球号的出现次数
        for ball in all_balls:
            if min_ball <= ball <= max_ball:
                times[int(ball)] += 1

        # 计算总次数
        total_count = sum(times.values())

        # 计算每个球号的出现概率
        probs = {ball: count / total_count if total_count > 0 else 0 for ball, count in times.items()}

        return times, probs

    def calculate_follow_statistics(self, df, columns, min_ball, max_ball):
        """
        计算球号的跟随性统计

        Args:
            df: 数据集
            columns: 要统计的列索引列表
            min_ball: 最小球号
            max_ball: 最大球号

        Returns:
            follow_time: 跟随次数矩阵
            follow_prob: 跟随概率矩阵
        """
        # 创建跟随次数矩阵和概率矩阵
        num_balls = max_ball - min_ball + 1
        follow_time = np.zeros((num_balls, num_balls), dtype=int)
        follow_prob = np.zeros((num_balls, num_balls), dtype=float)

        # 遍历数据集中的每一行（除了最后一行）
        for i in range(len(df) - 1):
            # 获取当前行和下一行的球号
            current_row_balls = set()
            next_row_balls = set()

            # 收集当前行的所有球号
            for col in columns:
                ball = df.iloc[i, col]
                if min_ball <= ball <= max_ball:
                    current_row_balls.add(int(ball))

            # 收集下一行的所有球号
            for col in columns:
                ball = df.iloc[i+1, col]
                if min_ball <= ball <= max_ball:
                    next_row_balls.add(int(ball))

            # 更新跟随次数矩阵
            for current_ball in current_row_balls:
                for next_ball in next_row_balls:
                    # 矩阵索引从0开始，所以需要减去min_ball
                    follow_time[next_ball - min_ball, current_ball - min_ball] += 1

        # 计算跟随概率矩阵
        for col in range(num_balls):
            col_sum = np.sum(follow_time[:, col])
            if col_sum > 0:
                follow_prob[:, col] = follow_time[:, col] / col_sum

        return follow_time, follow_prob

    def predict_numbers(self, train_data, method="multi_bayesian"):
        """
        根据训练数据预测下一期号码

        Args:
            train_data: 训练数据
            method: 预测方法

        Returns:
            predicted_numbers: 预测的号码列表
        """
        if len(train_data) < 10:  # 需要足够的训练数据
            return None

        if self.lottery_type == "SSQ":
            # 双色球：红球1-33，蓝球1-16
            red_columns = range(1, 7)  # 第2-7列是红球
            blue_columns = [7]  # 第8列是蓝球
            red_range = (1, 33)
            blue_range = (1, 16)
            red_count = 6
            blue_count = 1
        else:  # DLT
            # 大乐透：红球1-35，蓝球1-12
            red_columns = range(1, 6)  # 第2-6列是红球
            blue_columns = [6, 7]  # 第7-8列是蓝球
            red_range = (1, 35)
            blue_range = (1, 12)
            red_count = 5
            blue_count = 2

        # 统计红球和蓝球的出现次数和概率
        red_times, red_probs = self.calculate_ball_statistics(
            train_data, red_columns, red_range[0], red_range[1])
        blue_times, blue_probs = self.calculate_ball_statistics(
            train_data, blue_columns, blue_range[0], blue_range[1])

        # 统计跟随性
        red_follow_time, red_follow_prob = self.calculate_follow_statistics(
            train_data, red_columns, red_range[0], red_range[1])
        blue_follow_time, blue_follow_prob = self.calculate_follow_statistics(
            train_data, blue_columns, blue_range[0], blue_range[1])

        # 获取最新一期号码
        last_numbers = train_data.iloc[-1, 1:].values

        if method == "bayesian":
            # 基于贝叶斯概率预测
            red_prediction_probs = np.array([red_probs[i] for i in range(red_range[0], red_range[1] + 1)])
            blue_prediction_probs = np.array([blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)])

        elif method == "multi_bayesian":
            # 基于多条件贝叶斯概率预测
            red_prediction_probs = np.zeros(red_range[1] - red_range[0] + 1)
            blue_prediction_probs = np.zeros(blue_range[1] - blue_range[0] + 1)

            # 红球多条件贝叶斯概率
            red_probs_list = [red_probs[i] for i in range(red_range[0], red_range[1] + 1)]
            for i in range(red_range[1] - red_range[0] + 1):
                prob_sum = 0
                for j, col in enumerate(red_columns):
                    last_ball = last_numbers[col - 1]  # 获取最新一期对应位置的球号
                    if red_range[0] <= last_ball <= red_range[1]:
                        prob_sum += red_follow_prob[i, last_ball - red_range[0]] * red_probs_list[last_ball - red_range[0]]
                red_prediction_probs[i] = prob_sum

            # 蓝球多条件贝叶斯概率
            blue_probs_list = [blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)]
            for i in range(blue_range[1] - blue_range[0] + 1):
                prob_sum = 0
                for j, col in enumerate(blue_columns):
                    last_ball = last_numbers[col - 1]  # 获取最新一期对应位置的球号
                    if blue_range[0] <= last_ball <= blue_range[1]:
                        prob_sum += blue_follow_prob[i, last_ball - blue_range[0]] * blue_probs_list[last_ball - blue_range[0]]
                blue_prediction_probs[i] = prob_sum

        else:  # full_bayesian
            # 基于全条件贝叶斯概率预测
            red_prediction_probs = np.zeros(red_range[1] - red_range[0] + 1)
            blue_prediction_probs = np.zeros(blue_range[1] - blue_range[0] + 1)

            # 红球全条件贝叶斯概率
            red_probs_list = [red_probs[i] for i in range(red_range[0], red_range[1] + 1)]
            for i in range(red_range[1] - red_range[0] + 1):
                prob_sum = 0
                for j in range(red_range[1] - red_range[0] + 1):
                    prob_sum += red_follow_prob[i, j] * red_probs_list[j]
                red_prediction_probs[i] = prob_sum

            # 蓝球全条件贝叶斯概率
            blue_probs_list = [blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)]
            for i in range(blue_range[1] - blue_range[0] + 1):
                prob_sum = 0
                for j in range(blue_range[1] - blue_range[0] + 1):
                    prob_sum += blue_follow_prob[i, j] * blue_probs_list[j]
                blue_prediction_probs[i] = prob_sum

        # 选择概率最高的号码
        red_indices = np.argsort(red_prediction_probs)[-red_count:]
        red_numbers = sorted([i + red_range[0] for i in red_indices])

        blue_indices = np.argsort(blue_prediction_probs)[-blue_count:]
        blue_numbers = sorted([i + blue_range[0] for i in blue_indices])

        return red_numbers + blue_numbers

    def check_hit_rate(self, predicted, actual):
        """
        检查预测号码与实际号码的命中情况

        Args:
            predicted: 预测号码列表
            actual: 实际号码列表

        Returns:
            hit_info: 命中信息字典
        """
        if self.lottery_type == "SSQ":
            # 双色球：前6个是红球，第7个是蓝球
            pred_red = set(predicted[:6])
            pred_blue = set(predicted[6:7])
            actual_red = set(actual[:6])
            actual_blue = set(actual[6:7])

            red_hits = len(pred_red & actual_red)
            blue_hits = len(pred_blue & actual_blue)

            # SSQ中奖条件：红球至少5个相等，蓝球1个相等
            is_hit = red_hits >= 5 and blue_hits == 1

        else:  # DLT
            # 大乐透：前5个是红球，后2个是蓝球
            pred_red = set(predicted[:5])
            pred_blue = set(predicted[5:7])
            actual_red = set(actual[:5])
            actual_blue = set(actual[5:7])

            red_hits = len(pred_red & actual_red)
            blue_hits = len(pred_blue & actual_blue)

            # DLT中奖条件：红球至少4个相等，蓝球2个相等
            is_hit = red_hits >= 4 and blue_hits == 2

        return {
            'red_hits': red_hits,
            'blue_hits': blue_hits,
            'total_hits': red_hits + blue_hits,
            'is_hit': is_hit
        }

    def step3_calculate_and_compare(self):
        """
        第三步：开始计算和比对
        """
        print("\n" + "=" * 60)
        print("第三步：开始计算和比对")
        print("=" * 60)

        print(f"开始从第 {self.start_row} 行进行计算...")
        print(f"使用方法: {self.method}")
        print(f"将进行99次计算和比对...")

        self.results = []
        hit_count = 0

        for i in range(99):
            current_row = self.start_row + i

            # 检查是否超出数据范围
            if current_row >= len(self.data):
                print(f"警告: 第 {current_row} 行超出数据范围，停止计算")
                break

            # 获取训练数据（从开始到当前行）
            train_data = self.data.iloc[:current_row].copy()

            # 获取实际的下一期号码
            actual_numbers = self.data.iloc[current_row, 1:].values.tolist()

            # 预测下一期号码
            predicted_numbers = self.predict_numbers(train_data, self.method)

            if predicted_numbers is None:
                print(f"第 {i+1} 次计算失败：训练数据不足")
                continue

            # 检查命中情况
            hit_info = self.check_hit_rate(predicted_numbers, actual_numbers)

            # 记录结果
            result = {
                'iteration': i + 1,
                'base_period': self.data.iloc[current_row - 1, 0],  # 基于的期号
                'target_period': self.data.iloc[current_row, 0],    # 预测的期号
                'method': self.method,
                'predicted_numbers': predicted_numbers,
                'actual_numbers': actual_numbers,
                'red_hits': hit_info['red_hits'],
                'blue_hits': hit_info['blue_hits'],
                'total_hits': hit_info['total_hits'],
                'is_hit': hit_info['is_hit']
            }

            self.results.append(result)

            # 如果命中，打印结果
            if hit_info['is_hit']:
                hit_count += 1
                print(f"命中！第 {i+1} 次计算:")
                print(f"  基于第 {result['base_period']} 期号码")
                print(f"  用 {self.method} 方法预测")
                print(f"  与第 {result['target_period']} 期号码")
                print(f"  命中 {hit_info['total_hits']} 个号码")
                print(f"  (红球: {hit_info['red_hits']}, 蓝球: {hit_info['blue_hits']})")

            # 显示进度
            if (i + 1) % 10 == 0:
                print(f"已完成 {i + 1}/99 次计算，当前命中次数: {hit_count}")

        print(f"\n第三步完成！")
        print(f"总计算次数: {len(self.results)}")
        print(f"总命中次数: {hit_count}")
        print(f"命中率: {hit_count/len(self.results)*100:.2f}%" if self.results else "0%")

        return True

    def step4_save_results_to_excel(self):
        """
        第四步：将校核结果保存在Excel文件中
        """
        print("\n" + "=" * 60)
        print("第四步：将校核结果保存在Excel文件中")
        print("=" * 60)

        if not self.results:
            print("没有结果数据可保存")
            return False

        try:
            # 创建结果DataFrame
            results_data = []
            for result in self.results:
                row = {
                    '计算次序': result['iteration'],
                    '基于期号': result['base_period'],
                    '预测期号': result['target_period'],
                    '计算方法': result['method'],
                    '预测号码': str(result['predicted_numbers']),
                    '实际号码': str(result['actual_numbers']),
                    '红球命中数': result['red_hits'],
                    '蓝球命中数': result['blue_hits'],
                    '总命中数': result['total_hits'],
                    '是否命中': '是' if result['is_hit'] else '否'
                }
                results_data.append(row)

            results_df = pd.DataFrame(results_data)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"lottery_analysis_results_{self.lottery_type}_{timestamp}.xlsx"

            # 保存到Excel文件
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 保存详细结果
                results_df.to_excel(writer, sheet_name='详细结果', index=False)

                # 创建汇总统计
                total_calculations = len(self.results)
                total_hits = sum(1 for r in self.results if r['is_hit'])
                hit_rate = total_hits / total_calculations * 100 if total_calculations > 0 else 0

                summary_data = {
                    '统计项目': ['彩票类型', '计算方法', '开始行数', '总计算次数', '总命中次数', '命中率(%)'],
                    '数值': [self.lottery_type, self.method, self.start_row,
                            total_calculations, total_hits, f"{hit_rate:.2f}"]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)

                # 保存命中详情
                hit_results = [r for r in self.results if r['is_hit']]
                if hit_results:
                    hit_data = []
                    for result in hit_results:
                        row = {
                            '计算次序': result['iteration'],
                            '基于期号': result['base_period'],
                            '预测期号': result['target_period'],
                            '预测号码': str(result['predicted_numbers']),
                            '实际号码': str(result['actual_numbers']),
                            '命中详情': f"红球{result['red_hits']}个，蓝球{result['blue_hits']}个"
                        }
                        hit_data.append(row)

                    hit_df = pd.DataFrame(hit_data)
                    hit_df.to_excel(writer, sheet_name='命中详情', index=False)

            print(f"结果已保存到文件: {filename}")
            print(f"包含工作表: 详细结果、汇总统计" + ("、命中详情" if hit_results else ""))

            return True

        except Exception as e:
            print(f"保存结果时出错: {str(e)}")
            return False

    def run_analysis(self):
        """
        运行完整的四步分析流程
        """
        print("开始彩票数据分析四步程序")
        print("=" * 60)

        # 第一步：读取数据
        if not self.step1_read_and_sort_data():
            return False

        # 第二步：获取用户参数
        if not self.step2_get_user_parameters():
            return False

        # 第三步：计算和比对
        if not self.step3_calculate_and_compare():
            return False

        # 第四步：保存结果
        if not self.step4_save_results_to_excel():
            return False

        print("\n" + "=" * 60)
        print("四步分析流程全部完成！")
        print("=" * 60)

        return True

if __name__ == "__main__":
    # 创建分析器实例
    analyzer = LotteryAnalyzer()

    # 运行完整分析流程
    analyzer.run_analysis()
